package cn.com.chinastock.cnf.security.webflux.csrf;

import org.springframework.security.web.server.util.matcher.ServerWebExchangeMatcher;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

/**
 * WebFlux 环境下的自定义 CSRF 请求匹配器
 * 
 * <p>该类用于在 WebFlux 响应式环境中判断请求是否需要 CSRF 保护。</p>
 * <p>匹配逻辑：</p>
 * <ul>
 *     <li>如果请求匹配黑名单模式，则需要 CSRF 保护</li>
 *     <li>如果请求匹配白名单模式，则不需要 CSRF 保护</li>
 *     <li>其他情况默认需要 CSRF 保护</li>
 * </ul>
 * 
 * <AUTHOR> Boot Team
 */
public class ReactiveCustomCsrfRequestMatcher implements ServerWebExchangeMatcher {
    
    private final List<String> whitelistPatterns = new ArrayList<>();
    private final List<String> blacklistPatterns = new ArrayList<>();
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 构造函数
     * 
     * @param whitelist 白名单路径模式列表
     * @param blacklist 黑名单路径模式列表
     */
    public ReactiveCustomCsrfRequestMatcher(List<String> whitelist, List<String> blacklist) {
        if (whitelist != null) {
            this.whitelistPatterns.addAll(whitelist);
        }
        
        if (blacklist != null) {
            this.blacklistPatterns.addAll(blacklist);
        }
    }

    @Override
    public Mono<MatchResult> matches(ServerWebExchange exchange) {
        String requestPath = exchange.getRequest().getPath().value();
        
        // 检查黑名单，如果匹配则需要 CSRF 保护
        for (String blacklistPattern : blacklistPatterns) {
            if (pathMatcher.match(blacklistPattern, requestPath)) {
                return MatchResult.match();
            }
        }
        
        // 检查白名单，如果匹配则不需要 CSRF 保护
        for (String whitelistPattern : whitelistPatterns) {
            if (pathMatcher.match(whitelistPattern, requestPath)) {
                return MatchResult.notMatch();
            }
        }
        
        // 默认需要 CSRF 保护
        return MatchResult.match();
    }
}
