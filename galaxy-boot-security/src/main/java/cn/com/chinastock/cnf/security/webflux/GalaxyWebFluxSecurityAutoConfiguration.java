package cn.com.chinastock.cnf.security.webflux;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.security.GalaxySecurityProperties;
import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.webflux.csrf.ReactiveCustomCsrfRequestMatcher;
import cn.com.chinastock.cnf.security.webflux.input.XssWebFilter;
import cn.com.chinastock.cnf.security.webflux.output.XssResponseBodyAdvice;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.core.userdetails.MapReactiveUserDetailsService;
import org.springframework.security.core.userdetails.ReactiveUserDetailsService;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.header.ReferrerPolicyServerHttpHeadersWriter;
import org.springframework.security.web.server.header.XFrameOptionsServerHttpHeadersWriter;
import org.springframework.security.web.server.header.XXssProtectionServerHttpHeadersWriter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsConfigurationSource;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.security.config.Customizer.withDefaults;

/**
 * WebFlux 环境下的 Galaxy Security 自动配置类
 * 
 * <p>该配置类专门为 WebFlux 响应式环境提供安全配置，包括：</p>
 * <ul>
 *     <li>响应式安全过滤器链配置</li>
 *     <li>响应式 XSS 防护</li>
 *     <li>响应式 CSRF 防护</li>
 *     <li>响应式 CORS 配置</li>
 *     <li>响应式用户认证服务</li>
 * </ul>
 * 
 * <AUTHOR> Boot Team
 */
@AutoConfiguration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
@ConditionalOnClass({RouterFunction.class, ServerHttpSecurity.class})
@EnableConfigurationProperties({GalaxySecurityProperties.class, CopiedFastJsonProperties.class})
public class GalaxyWebFluxSecurityAutoConfiguration {
    
    private final GalaxySecurityProperties securityProperties;
    private final CopiedFastJsonProperties fastJsonProperties;

    public GalaxyWebFluxSecurityAutoConfiguration(GalaxySecurityProperties securityProperties, 
                                                  CopiedFastJsonProperties fastJsonProperties) {
        this.securityProperties = securityProperties;
        this.fastJsonProperties = fastJsonProperties;
    }

    /**
     * 配置 WebFlux 环境下的 XSS 响应体处理
     *
     * @return XSS 响应体处理器实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "output-protect", havingValue = "true")
    public XssResponseBodyAdvice xssResponseBodyAdvice() {
        return new XssResponseBodyAdvice(fastJsonProperties);
    }

    /**
     * 配置 WebFlux 环境下的 XSS 输入过滤器
     *
     * @return XSS Web 过滤器实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "input-protect", havingValue = "true")
    public XssWebFilter xssWebFilter() {
        return new XssWebFilter();
    }

    /**
     * 配置 WebFlux 安全过滤器链
     *
     * @param http ServerHttpSecurity 配置对象
     * @return 安全 Web 过滤器链实例
     */
    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "WebFlux securityProperties: " + securityProperties.toString());

        // 配置安全头
        http.headers(headers -> headers
                // 禁止页面在 frame 中加载
                .frameOptions(frame -> frame.mode(XFrameOptionsServerHttpHeadersWriter.Mode.DENY))
                // 控制浏览器在导航到其他页面时发送的 Referer 头信息
                .referrerPolicy(referrer -> referrer.policy(ReferrerPolicyServerHttpHeadersWriter.ReferrerPolicy.STRICT_ORIGIN))
        );

        // 配置授权规则
        if (securityProperties.getActuator().isProtect()) {
            List<String> whitelist = securityProperties.getActuator().getWhitelist();
            if (whitelist != null && !whitelist.isEmpty()) {
                String ipExpression = whitelist.stream()
                        .map(ip -> "hasIpAddress('" + ip + "')")
                        .collect(Collectors.joining(" or "));

                http.authorizeExchange(exchanges -> exchanges
                        .pathMatchers("/actuator/**").authenticated()
                        .anyExchange().permitAll()
                );
            } else {
                http.authorizeExchange(exchanges -> exchanges
                        .pathMatchers("/actuator/**").authenticated()
                        .anyExchange().permitAll()
                )
                .httpBasic(withDefaults());
            }
        } else {
            http.authorizeExchange(exchanges -> exchanges.anyExchange().permitAll());
        }

        // 配置 CSRF
        http.csrf(csrf -> {
            if (securityProperties.getCsrf().isProtect()) {
                List<String> whitelist = securityProperties.getCsrf().getWhitelist();
                List<String> blacklist = securityProperties.getCsrf().getBlacklist();
                csrf.requireCsrfProtectionMatcher(new ReactiveCustomCsrfRequestMatcher(whitelist, blacklist));
            } else {
                csrf.disable();
            }
        });

        return http.build();
    }

    /**
     * 配置 WebFlux 环境下的 CORS
     *
     * @return CORS 配置源实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "cors.protect", havingValue = "true")
    public CorsConfigurationSource corsConfigurationSource() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        List<String> whitelist = securityProperties.getCors().getWhitelist();
        
        CorsConfiguration configuration = new CorsConfiguration();
        if (whitelist != null && !whitelist.isEmpty()) {
            configuration.setAllowCredentials(true);
            configuration.setAllowedOriginPatterns(whitelist);
            configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
            configuration.setAllowedHeaders(List.of("*"));
        } else {
            configuration.setAllowCredentials(false);
            configuration.setAllowedOrigins(List.of());
            configuration.setAllowedMethods(List.of());
            configuration.setAllowedHeaders(List.of());
        }
        
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    /**
     * 配置响应式用户详情服务
     *
     * @return 响应式用户详情服务实例
     */
    @Bean
    @ConditionalOnClass(ReactiveUserDetailsService.class)
    public ReactiveUserDetailsService reactiveUserDetailsService() {
        String name = securityProperties.getUser().getName();
        String password = securityProperties.getUser().getPassword();

        if (name == null || password == null) {
            // 创建一个默认用户，避免 MapReactiveUserDetailsService 的空用户列表异常
            UserDetails defaultUser = User.withUsername("user")
                    .password(passwordEncoder().encode("password"))
                    .roles("USER")
                    .build();
            return new MapReactiveUserDetailsService(defaultUser);
        }

        String encodePassword = passwordEncoder().encode(password);
        UserDetails user = User.withUsername(name)
                .password(encodePassword)
                .roles("USER")
                .build();

        return new MapReactiveUserDetailsService(user);
    }

    /**
     * 密码编码器
     *
     * @return 密码编码器实例
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
