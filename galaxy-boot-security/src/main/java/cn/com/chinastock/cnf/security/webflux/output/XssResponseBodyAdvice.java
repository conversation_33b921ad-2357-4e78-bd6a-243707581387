package cn.com.chinastock.cnf.security.webflux.output;

import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.output.FastJsonFilterUtil;
import cn.com.chinastock.cnf.security.output.FasterJsonFilterUtil;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.codec.HttpMessageWriter;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.reactive.result.method.annotation.ResponseBodyResultHandler;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.HtmlUtils;
import reactor.core.publisher.Mono;

/**
 * WebFlux 环境下的 XSS 响应体处理器
 * 
 * <p>该类用于在 WebFlux 响应式环境中对响应体进行 XSS 防护处理。</p>
 * <p>主要功能包括：</p>
 * <ul>
 *     <li>JSON 响应的 XSS 过滤</li>
 *     <li>字符串响应的 HTML 转义</li>
 *     <li>支持 FastJSON 和 Jackson 序列化器</li>
 * </ul>
 * 
 * <p>注意：WebFlux 环境下的响应体处理与 WebMVC 不同，需要使用不同的机制。</p>
 * <p>这个类主要用于演示，实际的 WebFlux 响应体处理需要通过 WebFilter 或其他机制实现。</p>
 * 
 * <AUTHOR> Boot Team
 */
@ControllerAdvice
public class XssResponseBodyAdvice {
    
    private final CopiedFastJsonProperties fastJsonProperties;

    public XssResponseBodyAdvice(CopiedFastJsonProperties fastJsonProperties) {
        this.fastJsonProperties = fastJsonProperties;
    }

    /**
     * 处理响应体数据
     * 
     * <p>注意：在 WebFlux 环境中，这个方法的实现方式与 WebMVC 不同。</p>
     * <p>实际的响应体处理应该通过 WebFilter 或自定义的 HttpMessageWriter 来实现。</p>
     * 
     * @param body 响应体对象
     * @param returnType 返回类型参数
     * @param selectedContentType 选择的内容类型
     * @param selectedWriterType 选择的写入器类型
     * @param request 服务器请求
     * @param response 服务器响应
     * @return 处理后的响应体
     */
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageWriter<?>> selectedWriterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        if (body == null) {
            return null;
        }

        // 如果选择的 Content-Type 是 JSON 类型
        if (selectedContentType != null && selectedContentType.includes(MediaType.APPLICATION_JSON)) {
            String writerName = selectedWriterType.getName();
            
            // 根据不同的消息写入器进行处理
            if (writerName.contains("FastJsonHttpMessageWriter")) {
                return FastJsonFilterUtil.handleFastJsonResponse(body, fastJsonProperties);
            } else if (writerName.contains("Jackson2JsonMessageWriter")) {
                return FasterJsonFilterUtil.handleJacksonResponse(body);
            } else {
                // 其他场景，默认使用 Jackson 处理
                return FasterJsonFilterUtil.handleJacksonResponse(body);
            }
        }

        // 如果响应体是 String 类型，进行 HTML 转义处理
        if (body instanceof String) {
            return HtmlUtils.htmlEscape((String) body);
        }

        return body;
    }

    /**
     * 处理 Mono 类型的响应体
     * 
     * @param body Mono 响应体
     * @param returnType 返回类型参数
     * @param selectedContentType 选择的内容类型
     * @param selectedWriterType 选择的写入器类型
     * @param exchange 服务器交换对象
     * @return 处理后的 Mono 响应体
     */
    public Mono<Object> beforeBodyWriteMono(Mono<Object> body, MethodParameter returnType, 
                                           MediaType selectedContentType,
                                           Class<? extends HttpMessageWriter<?>> selectedWriterType,
                                           ServerWebExchange exchange) {
        return body.map(obj -> beforeBodyWrite(obj, returnType, selectedContentType, 
                                             selectedWriterType, 
                                             exchange.getRequest(), 
                                             exchange.getResponse()));
    }
}
