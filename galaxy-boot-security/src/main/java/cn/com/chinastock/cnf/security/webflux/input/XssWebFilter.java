package cn.com.chinastock.cnf.security.webflux.input;

import cn.com.chinastock.cnf.security.input.XssSanitizerUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Set;

/**
 * WebFlux 环境下的 XSS 输入验证过滤器
 * 
 * <p>该过滤器用于在 WebFlux 响应式环境中对输入数据进行 XSS 防护处理，包括：</p>
 * <ul>
 *     <li>查询参数的 XSS 清理</li>
 *     <li>请求头的 XSS 清理</li>
 *     <li>请求体的 XSS 清理（JSON 格式）</li>
 *     <li>静态资源请求的跳过处理</li>
 * </ul>
 * 
 * <AUTHOR> Boot Team
 */
public class XssWebFilter implements WebFilter {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final Set<String> STATIC_RESOURCE_EXTENSIONS = Set.of(
            ".css", ".js", ".png", ".jpg", ".gif", ".ico", ".svg",
            ".woff", ".woff2", ".ttf", ".eot", ".mp3", ".mp4",
            ".pdf", ".xlsx", ".doc", ".docx", ".xls", ".ppt", ".pptx", ".zip"
    );

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 跳过静态资源
        if (isStaticResource(request.getPath().value())) {
            return chain.filter(exchange);
        }
        
        // 创建装饰后的请求
        ServerHttpRequestDecorator decoratedRequest = new XssServerHttpRequestDecorator(request);
        ServerWebExchange decoratedExchange = exchange.mutate().request(decoratedRequest).build();
        
        return chain.filter(decoratedExchange);
    }
    
    /**
     * 判断是否为静态资源
     *
     * @param path 请求路径
     * @return 如果是静态资源返回 true，否则返回 false
     */
    private boolean isStaticResource(String path) {
        return STATIC_RESOURCE_EXTENSIONS.stream()
                .anyMatch(path::endsWith);
    }
    
    /**
     * XSS 防护的 ServerHttpRequest 装饰器
     */
    private static class XssServerHttpRequestDecorator extends ServerHttpRequestDecorator {
        
        public XssServerHttpRequestDecorator(ServerHttpRequest delegate) {
            super(delegate);
        }
        
        @Override
        public MultiValueMap<String, String> getQueryParams() {
            MultiValueMap<String, String> queryParams = super.getQueryParams();
            // 创建一个新的 LinkedMultiValueMap 来存储清理后的参数
            MultiValueMap<String, String> sanitizedParams = new org.springframework.util.LinkedMultiValueMap<>();

            queryParams.forEach((key, values) -> {
                for (String value : values) {
                    if (value != null) {
                        sanitizedParams.add(key, XssSanitizerUtil.stripXSS(value));
                    } else {
                        sanitizedParams.add(key, null);
                    }
                }
            });

            return sanitizedParams;
        }
        
        @Override
        public Flux<DataBuffer> getBody() {
            return super.getBody()
                    .collectList()
                    .flatMapMany(dataBuffers -> {
                        if (dataBuffers.isEmpty()) {
                            return Flux.empty();
                        }
                        
                        // 合并所有 DataBuffer
                        DataBuffer joinedBuffer = dataBuffers.get(0).factory().join(dataBuffers);
                        
                        try {
                            // 读取请求体内容
                            byte[] bytes = new byte[joinedBuffer.readableByteCount()];
                            joinedBuffer.read(bytes);
                            String body = new String(bytes, StandardCharsets.UTF_8);
                            
                            // 清理 XSS
                            String sanitizedBody = sanitizeRequestBody(body);
                            
                            // 创建新的 DataBuffer
                            DataBufferFactory bufferFactory = joinedBuffer.factory();
                            DataBuffer sanitizedBuffer = bufferFactory.wrap(sanitizedBody.getBytes(StandardCharsets.UTF_8));
                            
                            return Flux.just(sanitizedBuffer);
                        } catch (Exception e) {
                            // 如果处理失败，返回原始数据
                            return Flux.just(joinedBuffer);
                        } finally {
                            // 释放原始 buffer
                            DataBufferUtils.release(joinedBuffer);
                        }
                    });
        }
        
        /**
         * 清理请求体中的 XSS
         *
         * @param requestBody 原始请求体内容
         * @return 清理后的请求体内容
         */
        private String sanitizeRequestBody(String requestBody) {
            if (requestBody == null || requestBody.isEmpty()) {
                return requestBody;
            }
            
            try {
                JsonNode jsonNode = OBJECT_MAPPER.readTree(requestBody);
                sanitizeJsonNode(jsonNode);
                return OBJECT_MAPPER.writeValueAsString(jsonNode);
            } catch (Exception e) {
                // 如果不是 JSON 格式，直接对整个字符串进行清理
                return XssSanitizerUtil.stripXSS(requestBody);
            }
        }
        
        /**
         * 递归清理 JSON 节点中的 XSS
         *
         * @param node 要清理的 JSON 节点
         */
        private void sanitizeJsonNode(JsonNode node) {
            if (node.isObject()) {
                ObjectNode objectNode = (ObjectNode) node;
                objectNode.fields().forEachRemaining(entry -> {
                    JsonNode value = entry.getValue();
                    if (value.isTextual()) {
                        String sanitizedText = XssSanitizerUtil.stripXSS(value.asText());
                        objectNode.set(entry.getKey(), new TextNode(sanitizedText));
                    } else {
                        sanitizeJsonNode(value);
                    }
                });
            } else if (node.isArray()) {
                ArrayNode arrayNode = (ArrayNode) node;
                for (int i = 0; i < arrayNode.size(); i++) {
                    JsonNode element = arrayNode.get(i);
                    if (element.isTextual()) {
                        String sanitizedText = XssSanitizerUtil.stripXSS(element.asText());
                        arrayNode.set(i, new TextNode(sanitizedText));
                    } else {
                        sanitizeJsonNode(element);
                    }
                }
            }
        }
    }
}
