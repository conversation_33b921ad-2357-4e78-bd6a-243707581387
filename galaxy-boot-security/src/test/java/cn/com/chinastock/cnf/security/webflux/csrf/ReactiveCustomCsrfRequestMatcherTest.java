package cn.com.chinastock.cnf.security.webflux.csrf;

import org.junit.jupiter.api.Test;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.security.web.server.util.matcher.ServerWebExchangeMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.List;

/**
 * 响应式自定义 CSRF 请求匹配器测试
 * 
 * <AUTHOR> Boot Team
 */
class ReactiveCustomCsrfRequestMatcherTest {

    @Test
    void should_match_when_request_matches_blacklist() {
        // Given
        List<String> whitelist = Arrays.asList("/public/**");
        List<String> blacklist = Arrays.asList("/secure/**");
        ReactiveCustomCsrfRequestMatcher matcher = new ReactiveCustomCsrfRequestMatcher(whitelist, blacklist);

        MockServerHttpRequest request = MockServerHttpRequest.get("/secure/resource").build();
        ServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(matcher.matches(exchange))
                .expectNextMatches(result -> result.isMatch())
                .verifyComplete();
    }

    @Test
    void should_not_match_when_request_matches_whitelist() {
        // Given
        List<String> whitelist = Arrays.asList("/public/**");
        List<String> blacklist = Arrays.asList("/secure/**");
        ReactiveCustomCsrfRequestMatcher matcher = new ReactiveCustomCsrfRequestMatcher(whitelist, blacklist);

        MockServerHttpRequest request = MockServerHttpRequest.get("/public/resource").build();
        ServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(matcher.matches(exchange))
                .expectNextMatches(result -> !result.isMatch())
                .verifyComplete();
    }

    @Test
    void should_match_when_request_does_not_match_whitelist_or_blacklist() {
        // Given
        List<String> whitelist = Arrays.asList("/public/**");
        List<String> blacklist = Arrays.asList("/secure/**");
        ReactiveCustomCsrfRequestMatcher matcher = new ReactiveCustomCsrfRequestMatcher(whitelist, blacklist);

        MockServerHttpRequest request = MockServerHttpRequest.get("/other/resource").build();
        ServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(matcher.matches(exchange))
                .expectNextMatches(result -> result.isMatch())
                .verifyComplete();
    }

    @Test
    void should_handle_null_whitelist_and_blacklist() {
        // Given
        ReactiveCustomCsrfRequestMatcher matcher = new ReactiveCustomCsrfRequestMatcher(null, null);

        MockServerHttpRequest request = MockServerHttpRequest.get("/any/resource").build();
        ServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(matcher.matches(exchange))
                .expectNextMatches(result -> result.isMatch())
                .verifyComplete();
    }

    @Test
    void should_handle_empty_whitelist_and_blacklist() {
        // Given
        List<String> whitelist = Arrays.asList();
        List<String> blacklist = Arrays.asList();
        ReactiveCustomCsrfRequestMatcher matcher = new ReactiveCustomCsrfRequestMatcher(whitelist, blacklist);

        MockServerHttpRequest request = MockServerHttpRequest.get("/any/resource").build();
        ServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then
        StepVerifier.create(matcher.matches(exchange))
                .expectNextMatches(result -> result.isMatch())
                .verifyComplete();
    }

    @Test
    void should_prioritize_blacklist_over_whitelist() {
        // Given - 同一个路径既在白名单又在黑名单中
        List<String> whitelist = Arrays.asList("/api/**");
        List<String> blacklist = Arrays.asList("/api/secure/**");
        ReactiveCustomCsrfRequestMatcher matcher = new ReactiveCustomCsrfRequestMatcher(whitelist, blacklist);

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/secure/resource").build();
        ServerWebExchange exchange = MockServerWebExchange.from(request);

        // When & Then - 黑名单优先，应该匹配（需要 CSRF 保护）
        StepVerifier.create(matcher.matches(exchange))
                .expectNextMatches(result -> result.isMatch())
                .verifyComplete();
    }
}
