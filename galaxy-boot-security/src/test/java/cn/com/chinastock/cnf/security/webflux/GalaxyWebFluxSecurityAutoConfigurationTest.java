package cn.com.chinastock.cnf.security.webflux;

import cn.com.chinastock.cnf.security.GalaxySecurityProperties;
import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.webflux.input.XssWebFilter;
import cn.com.chinastock.cnf.security.webflux.output.XssResponseBodyAdvice;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.autoconfigure.security.reactive.ReactiveSecurityAutoConfiguration;
import org.springframework.boot.test.context.runner.ReactiveWebApplicationContextRunner;
import org.springframework.security.core.userdetails.ReactiveUserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.web.cors.reactive.CorsConfigurationSource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * WebFlux 安全自动配置测试
 * 
 * <AUTHOR> Boot Team
 */
class GalaxyWebFluxSecurityAutoConfigurationTest {

    private final ReactiveWebApplicationContextRunner contextRunner = new ReactiveWebApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(
                    ReactiveSecurityAutoConfiguration.class,
                    GalaxyWebFluxSecurityAutoConfiguration.class
            ));

    @Test
    void should_create_security_web_filter_chain() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(SecurityWebFilterChain.class);
            assertThat(context).hasSingleBean(PasswordEncoder.class);
            assertThat(context).hasSingleBean(ReactiveUserDetailsService.class);
        });
    }

    @Test
    void should_create_xss_web_filter_when_input_protect_enabled() {
        contextRunner
                .withPropertyValues("galaxy.security.input-protect=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(XssWebFilter.class);
                });
    }

    @Test
    void should_not_create_xss_web_filter_when_input_protect_disabled() {
        contextRunner
                .withPropertyValues("galaxy.security.input-protect=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(XssWebFilter.class);
                });
    }

    @Test
    void should_create_xss_response_body_advice_when_output_protect_enabled() {
        contextRunner
                .withPropertyValues("galaxy.security.output-protect=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(XssResponseBodyAdvice.class);
                });
    }

    @Test
    void should_not_create_xss_response_body_advice_when_output_protect_disabled() {
        contextRunner
                .withPropertyValues("galaxy.security.output-protect=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(XssResponseBodyAdvice.class);
                });
    }

    @Test
    void should_create_cors_configuration_source_when_cors_protect_enabled() {
        contextRunner
                .withPropertyValues("galaxy.security.cors.protect=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(CorsConfigurationSource.class);
                });
    }

    @Test
    void should_not_create_cors_configuration_source_when_cors_protect_disabled() {
        contextRunner
                .withPropertyValues("galaxy.security.cors.protect=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(CorsConfigurationSource.class);
                });
    }

    @Test
    void should_configure_reactive_user_details_service_with_user_credentials() {
        contextRunner
                .withPropertyValues(
                        "galaxy.security.user.name=testuser",
                        "galaxy.security.user.password=testpass"
                )
                .run(context -> {
                    assertThat(context).hasSingleBean(ReactiveUserDetailsService.class);
                    ReactiveUserDetailsService userDetailsService = context.getBean(ReactiveUserDetailsService.class);
                    assertThat(userDetailsService).isNotNull();
                });
    }

    @Test
    void should_configure_empty_reactive_user_details_service_without_user_credentials() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(ReactiveUserDetailsService.class);
            ReactiveUserDetailsService userDetailsService = context.getBean(ReactiveUserDetailsService.class);
            assertThat(userDetailsService).isNotNull();
        });
    }
}
