# Galaxy Boot Security WebFlux 支持

## 概述

Galaxy Boot Security 现在同时支持 Servlet 和 WebFlux 环境。该模块会根据应用的类型自动选择合适的安全配置。

## 功能特性

### WebFlux 环境支持的功能

1. **响应式安全过滤器链**
   - 自动配置 `SecurityWebFilterChain`
   - 支持响应式的认证和授权

2. **响应式 XSS 防护**
   - `XssWebFilter`: WebFlux 环境下的 XSS 输入过滤
   - `XssResponseBodyAdvice`: 响应体 XSS 防护

3. **响应式 CSRF 防护**
   - `ReactiveCustomCsrfRequestMatcher`: 支持白名单和黑名单配置
   - 与 Servlet 版本相同的配置方式

4. **响应式 CORS 配置**
   - 使用 `CorsConfigurationSource` 进行 CORS 配置
   - 支持响应式的跨域请求处理

5. **响应式用户认证**
   - `ReactiveUserDetailsService`: 响应式用户详情服务
   - 支持内存用户配置

## 使用方式

### 1. 添加依赖

在 WebFlux 项目中添加 galaxy-boot-security 依赖：

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-security</artifactId>
</dependency>

<!-- WebFlux 依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-webflux</artifactId>
</dependency>
```

### 2. 配置文件

WebFlux 环境下的配置与 Servlet 环境完全相同：

```yaml
galaxy:
  security:
    # XSS 输入防护
    input-protect: true
    # XSS 输出防护
    output-protect: true
    
    # CSRF 防护配置
    csrf:
      protect: true
      whitelist:
        - "/public/**"
        - "/api/health"
      blacklist:
        - "/api/admin/**"
    
    # CORS 配置
    cors:
      protect: true
      whitelist:
        - "http://localhost:3000"
        - "https://*.example.com"
    
    # Actuator 保护
    actuator:
      protect: true
      whitelist:
        - "127.0.0.1"
        - "***********/24"
    
    # 用户配置
    user:
      name: admin
      password: secret
```

### 3. 示例应用

```java
@SpringBootApplication
public class WebFluxSecurityExampleApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(WebFluxSecurityExampleApplication.class, args);
    }
}

@RestController
@RequestMapping("/api")
public class ExampleController {
    
    @GetMapping("/public/hello")
    public Mono<String> publicHello() {
        return Mono.just("Hello, Public!");
    }
    
    @PostMapping("/secure/data")
    public Mono<Map<String, Object>> secureData(@RequestBody Map<String, Object> data) {
        // 输入数据会自动进行 XSS 清理
        return Mono.just(Map.of(
            "received", data,
            "timestamp", System.currentTimeMillis()
        ));
    }
    
    @GetMapping("/admin/users")
    public Flux<String> adminUsers() {
        // 需要认证的管理员接口
        return Flux.just("user1", "user2", "user3");
    }
}
```

## 自动配置原理

### 条件配置

Galaxy Boot Security 使用条件注解来区分不同的 Web 环境：

```java
// Servlet 环境配置
@AutoConfiguration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@ConditionalOnClass(name = "jakarta.servlet.Filter")
public class GalaxySecurityAutoConfiguration {
    // Servlet 相关配置
}

// WebFlux 环境配置
@AutoConfiguration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
@ConditionalOnClass({RouterFunction.class, ServerHttpSecurity.class})
public class GalaxyWebFluxSecurityAutoConfiguration {
    // WebFlux 相关配置
}
```

### 组件对应关系

| 功能 | Servlet 环境 | WebFlux 环境 |
|------|-------------|-------------|
| 安全配置 | `SecurityFilterChain` | `SecurityWebFilterChain` |
| XSS 输入过滤 | `XssInputValidationFilter` | `XssWebFilter` |
| CSRF 匹配 | `CustomCsrfRequestMatcher` | `ReactiveCustomCsrfRequestMatcher` |
| CORS 配置 | `CorsFilter` | `CorsConfigurationSource` |
| 用户服务 | `UserDetailsService` | `ReactiveUserDetailsService` |

## 注意事项

1. **环境检测**: 系统会自动检测应用类型，无需手动配置
2. **配置兼容**: WebFlux 和 Servlet 环境使用相同的配置属性
3. **功能对等**: 两种环境提供相同的安全功能
4. **性能优化**: WebFlux 环境下的组件针对响应式编程进行了优化

## 测试

### 单元测试

```java
@Test
void should_create_security_web_filter_chain() {
    new ReactiveWebApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxyWebFluxSecurityAutoConfiguration.class))
            .run(context -> {
                assertThat(context).hasSingleBean(SecurityWebFilterChain.class);
                assertThat(context).hasSingleBean(ReactiveUserDetailsService.class);
            });
}
```

### 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "galaxy.security.input-protect=true",
    "galaxy.security.csrf.protect=true"
})
class WebFluxSecurityIntegrationTest {
    
    @Autowired
    private WebTestClient webTestClient;
    
    @Test
    void should_protect_against_xss() {
        webTestClient.post()
                .uri("/api/secure/data")
                .bodyValue(Map.of("message", "<script>alert('xss')</script>"))
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.received.message").value(not(containsString("<script>")));
    }
}
```

## 迁移指南

### 从 Servlet 迁移到 WebFlux

1. **更新依赖**: 将 `spring-boot-starter-web` 替换为 `spring-boot-starter-webflux`
2. **保持配置**: 配置文件无需修改
3. **更新控制器**: 使用 `Mono` 和 `Flux` 替换同步返回类型
4. **测试验证**: 使用 `WebTestClient` 进行测试

### 混合环境

如果项目同时需要 Servlet 和 WebFlux 功能，系统会根据主要的 Web 环境类型选择对应的安全配置。
